import {
  DashboardOutlined,
  UserOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  MessageOutlined,
  CreditCardOutlined,
  TruckOutlined,
  BellOutlined,
  CommentOutlined,
  SearchOutlined,
  SettingOutlined,
  TeamOutlined,
  FileSearchOutlined,
  DatabaseOutlined,
  InfoCircleOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import type { MenuItem } from '../types';

export const menuItems: MenuItem[] = [
  {
    key: 'dashboard',
    icon: <DashboardOutlined />,
    label: '仪表盘',
    path: '/dashboard',
  },
  {
    key: 'accounts',
    icon: <UserOutlined />,
    label: '账号管理',
    path: '/accounts',
  },
  {
    key: 'items',
    icon: <ShoppingOutlined />,
    label: '商品管理',
    path: '/items',
  },
  {
    key: 'orders',
    icon: <FileTextOutlined />,
    label: '订单管理',
    path: '/orders',
  },
  {
    key: 'auto-reply',
    icon: <MessageOutlined />,
    label: '自动回复',
    path: '/auto-reply',
  },
  {
    key: 'items-reply',
    icon: <CommentOutlined />,
    label: '指定商品回复',
    path: '/items-reply',
  },
  {
    key: 'cards',
    icon: <CreditCardOutlined />,
    label: '卡券管理',
    path: '/cards',
  },
  {
    key: 'auto-delivery',
    icon: <TruckOutlined />,
    label: '自动发货',
    path: '/auto-delivery',
  },
  {
    key: 'notification-channels',
    icon: <BellOutlined />,
    label: '通知渠道',
    path: '/notification-channels',
  },
  {
    key: 'message-notifications',
    icon: <CommentOutlined />,
    label: '消息通知',
    path: '/message-notifications',
  },
  {
    key: 'item-search',
    icon: <SearchOutlined />,
    label: '商品搜索',
    path: '/item-search',
  },
  {
    key: 'system-settings',
    icon: <SettingOutlined />,
    label: '系统设置',
    path: '/system-settings',
  },
];

// 管理员专用菜单
export const adminMenuItems: MenuItem[] = [
  {
    key: 'user-management',
    icon: <TeamOutlined />,
    label: '用户管理',
    path: '/user-management',
  },
  {
    key: 'logs',
    icon: <FileSearchOutlined />,
    label: '系统日志',
    path: '/logs',
  },
  {
    key: 'data-management',
    icon: <DatabaseOutlined />,
    label: '数据管理',
    path: '/data-management',
  },
];

// 其他菜单项
export const otherMenuItems: MenuItem[] = [
  {
    key: 'about',
    icon: <InfoCircleOutlined />,
    label: '关于',
    path: '/about',
  },
];

// 系统操作菜单
export const systemMenuItems: MenuItem[] = [
  {
    key: 'logout',
    icon: <LogoutOutlined />,
    label: '登出',
    path: '/logout',
  },
];

export default menuItems;
