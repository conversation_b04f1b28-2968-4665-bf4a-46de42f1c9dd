/* 全局样式重置 */
* {
  box-sizing: border-box;
}

#root {
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 24px;
  padding: 0 0 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-info h2 {
  margin-bottom: 4px !important;
}

.header-extra {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 12px;
  }

  .header-extra {
    align-self: flex-start;
  }
}

/* 表格响应式 */
.ant-table-wrapper {
  overflow-x: auto;
}

/* 卡片间距 */
.ant-card + .ant-card {
  margin-top: 16px;
}

/* 统计卡片样式 */
.ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 4px;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
