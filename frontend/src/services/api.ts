import axios from 'axios';
import { message } from 'antd';
import type { 
  ApiResponse, 
  Account, 
  Keyword, 
  Item, 
  Order, 
  Card, 
  DeliveryRule, 
  NotificationChannel, 
  MessageNotification,
  DashboardStats,
  PaginationParams,
  SearchParams
} from '../types';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://192.168.109.129:8080',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      window.location.href = '/login.html';
    } else if (error.response?.status >= 500) {
      message.error('服务器错误，请稍后重试');
    } else if (error.message === 'Network Error') {
      message.error('网络连接失败，请检查网络');
    }
    return Promise.reject(error);
  }
);

// 通用请求方法
const request = async <T>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  url: string,
  data?: any,
  params?: any
): Promise<ApiResponse<T>> => {
  try {
    const response = await api.request({
      method,
      url,
      data,
      params,
    });
    return response.data;
  } catch (error: any) {
    throw error.response?.data || error;
  }
};

// 账号管理API
export const accountApi = {
  // 获取账号列表
  getAccounts: () => request<Account[]>('GET', '/api/cookies'),
  
  // 添加账号
  addAccount: (data: { id: string; cookie_value: string }) => 
    request('POST', '/api/cookies', data),
  
  // 更新账号
  updateAccount: (id: string, data: Partial<Account>) => 
    request('PUT', `/api/cookies/${id}`, data),
  
  // 删除账号
  deleteAccount: (id: string) => 
    request('DELETE', `/api/cookies/${id}`),
  
  // 获取默认回复
  getDefaultReplies: (cookieId: string) => 
    request('GET', `/api/cookies/${cookieId}/default-replies`),
  
  // 更新默认回复
  updateDefaultReplies: (cookieId: string, replies: string[]) => 
    request('PUT', `/api/cookies/${cookieId}/default-replies`, { replies }),
};

// 关键词管理API
export const keywordApi = {
  // 获取关键词列表
  getKeywords: (cookieId: string) => 
    request<Keyword[]>('GET', `/api/keywords/${cookieId}`),
  
  // 添加关键词
  addKeyword: (data: Omit<Keyword, 'id' | 'created_at' | 'updated_at'>) => 
    request('POST', '/api/keywords', data),
  
  // 更新关键词
  updateKeyword: (id: string, data: Partial<Keyword>) => 
    request('PUT', `/api/keywords/${id}`, data),
  
  // 删除关键词
  deleteKeyword: (id: string) => 
    request('DELETE', `/api/keywords/${id}`),
  
  // 批量删除关键词
  batchDeleteKeywords: (ids: string[]) => 
    request('DELETE', '/api/keywords/batch', { ids }),
  
  // 导出关键词
  exportKeywords: (cookieId: string) => 
    request('GET', `/api/keywords/${cookieId}/export`),
  
  // 导入关键词
  importKeywords: (cookieId: string, data: any) => 
    request('POST', `/api/keywords/${cookieId}/import`, data),
  
  // 上传图片关键词
  uploadImageKeyword: (formData: FormData) => 
    api.post('/api/keywords/upload-image', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
};

// 商品管理API
export const itemApi = {
  // 获取商品列表
  getItems: (params?: SearchParams & PaginationParams) => 
    request<{ items: Item[]; total: number }>('GET', '/api/items', null, params),
  
  // 获取指定账号商品
  getItemsByAccount: (cookieId: string, page?: number) => 
    request('GET', `/api/items/${cookieId}`, null, { page }),
  
  // 获取所有页商品
  getAllItemsFromAccount: (cookieId: string) => 
    request('GET', `/api/items/${cookieId}/all`),
  
  // 更新商品
  updateItem: (id: string, data: Partial<Item>) => 
    request('PUT', `/api/items/${id}`, data),
  
  // 删除商品
  deleteItem: (id: string) => 
    request('DELETE', `/api/items/${id}`),
  
  // 批量删除商品
  batchDeleteItems: (ids: string[]) => 
    request('DELETE', '/api/items/batch', { ids }),
};

// 订单管理API
export const orderApi = {
  // 获取订单列表
  getOrders: (params?: SearchParams & PaginationParams) => 
    request<{ orders: Order[]; total: number }>('GET', '/api/orders', null, params),
  
  // 更新订单状态
  updateOrderStatus: (id: string, status: Order['status']) => 
    request('PUT', `/api/orders/${id}/status`, { status }),
  
  // 删除订单
  deleteOrder: (id: string) => 
    request('DELETE', `/api/orders/${id}`),
  
  // 批量删除订单
  batchDeleteOrders: (ids: string[]) => 
    request('DELETE', '/api/orders/batch', { ids }),
};

// 仪表盘API
export const dashboardApi = {
  // 获取仪表盘数据
  getDashboardData: () => 
    request<DashboardStats>('GET', '/api/dashboard'),
  
  // 获取系统版本
  getSystemVersion: () => 
    request<{ version: string }>('GET', '/api/version'),
};

// 卡券管理API
export const cardApi = {
  // 获取卡券列表
  getCards: () => request<Card[]>('GET', '/api/cards'),

  // 添加卡券
  addCard: (data: Omit<Card, 'id' | 'created_at' | 'updated_at'>) =>
    request('POST', '/api/cards', data),

  // 更新卡券
  updateCard: (id: string, data: Partial<Card>) =>
    request('PUT', `/api/cards/${id}`, data),

  // 删除卡券
  deleteCard: (id: string) =>
    request('DELETE', `/api/cards/${id}`),
};

// 发货规则API
export const deliveryApi = {
  // 获取发货规则列表
  getDeliveryRules: () => request<DeliveryRule[]>('GET', '/api/delivery-rules'),

  // 添加发货规则
  addDeliveryRule: (data: Omit<DeliveryRule, 'id' | 'created_at' | 'updated_at'>) =>
    request('POST', '/api/delivery-rules', data),

  // 更新发货规则
  updateDeliveryRule: (id: string, data: Partial<DeliveryRule>) =>
    request('PUT', `/api/delivery-rules/${id}`, data),

  // 删除发货规则
  deleteDeliveryRule: (id: string) =>
    request('DELETE', `/api/delivery-rules/${id}`),
};

// 通知渠道API
export const notificationApi = {
  // 获取通知渠道列表
  getChannels: () => request<NotificationChannel[]>('GET', '/api/notification-channels'),

  // 添加通知渠道
  addChannel: (data: Omit<NotificationChannel, 'id' | 'created_at' | 'updated_at'>) =>
    request('POST', '/api/notification-channels', data),

  // 更新通知渠道
  updateChannel: (id: string, data: Partial<NotificationChannel>) =>
    request('PUT', `/api/notification-channels/${id}`, data),

  // 删除通知渠道
  deleteChannel: (id: string) =>
    request('DELETE', `/api/notification-channels/${id}`),

  // 测试通知渠道
  testChannel: (id: string) =>
    request('POST', `/api/notification-channels/${id}/test`),

  // 获取消息通知配置
  getMessageNotifications: () => request<MessageNotification[]>('GET', '/api/message-notifications'),

  // 更新消息通知配置
  updateMessageNotification: (cookieId: string, channelId: string, enabled: boolean) =>
    request('PUT', `/api/message-notifications/${cookieId}/${channelId}`, { enabled }),
};

// 认证相关API
export const authApi = {
  // 登录
  login: (data: { username: string; password: string }) =>
    api.post('/auth/login', data),

  // 登出
  logout: () =>
    api.post('/auth/logout'),

  // 获取用户信息
  getUserInfo: () =>
    api.get('/auth/user'),

  // 刷新token
  refreshToken: () =>
    api.post('/auth/refresh'),
};

export default api;
