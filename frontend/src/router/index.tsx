import { createBrowserRouter, Navigate } from 'react-router-dom';
import MainLayout from '../components/Layout/MainLayout';
import Dashboard from '../pages/Dashboard';
import AccountManagement from '../pages/AccountManagement';
import ItemManagement from '../pages/ItemManagement';
import OrderManagement from '../pages/OrderManagement';
import AutoReply from '../pages/AutoReply';
import ItemReply from '../pages/ItemReply';
import CardManagement from '../pages/CardManagement';
import AutoDelivery from '../pages/AutoDelivery';
import NotificationChannels from '../pages/NotificationChannels';
import MessageNotifications from '../pages/MessageNotifications';
import ItemSearch from '../pages/ItemSearch';
import SystemSettings from '../pages/SystemSettings';
import UserManagement from '../pages/UserManagement';
import SystemLogs from '../pages/SystemLogs';
import DataManagement from '../pages/DataManagement';
import About from '../pages/About';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <MainLayout />,
    children: [
      {
        index: true,
        element: <Navigate to="/dashboard" replace />,
      },
      {
        path: 'dashboard',
        element: <Dashboard />,
      },
      {
        path: 'accounts',
        element: <AccountManagement />,
      },
      {
        path: 'items',
        element: <ItemManagement />,
      },
      {
        path: 'orders',
        element: <OrderManagement />,
      },
      {
        path: 'auto-reply',
        element: <AutoReply />,
      },
      {
        path: 'items-reply',
        element: <ItemReply />,
      },
      {
        path: 'cards',
        element: <CardManagement />,
      },
      {
        path: 'auto-delivery',
        element: <AutoDelivery />,
      },
      {
        path: 'notification-channels',
        element: <NotificationChannels />,
      },
      {
        path: 'message-notifications',
        element: <MessageNotifications />,
      },
      {
        path: 'item-search',
        element: <ItemSearch />,
      },
      {
        path: 'system-settings',
        element: <SystemSettings />,
      },
      {
        path: 'user-management',
        element: <UserManagement />,
      },
      {
        path: 'logs',
        element: <SystemLogs />,
      },
      {
        path: 'data-management',
        element: <DataManagement />,
      },
      {
        path: 'about',
        element: <About />,
      },
    ],
  },
]);

export default router;
