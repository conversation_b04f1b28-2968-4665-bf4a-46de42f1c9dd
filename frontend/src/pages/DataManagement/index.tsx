import React from 'react';
import { Card, Typography } from 'antd';
import { DatabaseOutlined } from '@ant-design/icons';

const { Title } = Typography;

const DataManagement: React.FC = () => {
  return (
    <div>
      <div className="page-header">
        <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
          <DatabaseOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          数据管理
        </Title>
      </div>
      <Card>
        <p>数据管理功能正在开发中...</p>
      </Card>
    </div>
  );
};

export default DataManagement;
