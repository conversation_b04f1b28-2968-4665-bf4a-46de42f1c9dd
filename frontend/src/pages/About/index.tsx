import React from 'react';
import { Card, Typography } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

const { Title } = Typography;

const About: React.FC = () => {
  return (
    <div>
      <div className="page-header">
        <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
          <InfoCircleOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          关于
        </Title>
      </div>
      <Card>
        <p>关于页面正在开发中...</p>
      </Card>
    </div>
  );
};

export default About;
