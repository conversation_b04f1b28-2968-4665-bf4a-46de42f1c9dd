import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Table,
  Form,
  Input,
  Switch,
  Space,
  Popconfirm,
  message,
  Modal,
  Tag,
  Tooltip,
  Row,
  Col,
  Alert,
  InputNumber,
  Spin,
} from 'antd';
import {
  UserOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  QrcodeOutlined,
  KeyboardOutlined,
  SettingOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { accountApi } from '../../services/api';
import type { Account } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;

const AccountManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [defaultReplyModalVisible, setDefaultReplyModalVisible] = useState(false);
  const [showManualInput, setShowManualInput] = useState(false);
  const [currentAccount, setCurrentAccount] = useState<Account | null>(null);
  const [defaultReplies, setDefaultReplies] = useState<string[]>([]);

  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 加载账号列表
  const loadAccounts = async () => {
    try {
      setLoading(true);
      const response = await accountApi.getAccounts();
      if (response.success) {
        setAccounts(response.data || []);
      }
    } catch (error) {
      console.error('加载账号列表失败:', error);
      message.error('加载账号列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAccounts();
  }, []);

  // 添加账号
  const handleAddAccount = async (values: { id: string; cookie_value: string }) => {
    try {
      const response = await accountApi.addAccount(values);
      if (response.success) {
        message.success(`账号 "${values.id}" 添加成功`);
        setAddModalVisible(false);
        addForm.resetFields();
        setShowManualInput(false);
        loadAccounts();
      }
    } catch (error: any) {
      message.error(error.message || '添加账号失败');
    }
  };

  // 更新账号
  const handleUpdateAccount = async (values: Partial<Account>) => {
    if (!currentAccount) return;

    try {
      const response = await accountApi.updateAccount(currentAccount.id, values);
      if (response.success) {
        message.success('账号信息更新成功');
        setEditModalVisible(false);
        editForm.resetFields();
        setCurrentAccount(null);
        loadAccounts();
      }
    } catch (error: any) {
      message.error(error.message || '更新账号失败');
    }
  };

  // 删除账号
  const handleDeleteAccount = async (id: string) => {
    try {
      const response = await accountApi.deleteAccount(id);
      if (response.success) {
        message.success(`账号 "${id}" 已删除`);
        loadAccounts();
      }
    } catch (error: any) {
      message.error(error.message || '删除账号失败');
    }
  };

  // 打开编辑模态框
  const openEditModal = (account: Account) => {
    setCurrentAccount(account);
    editForm.setFieldsValue({
      status: account.status,
      default_reply: account.default_reply,
      ai_reply: account.ai_reply,
      auto_confirm_delivery: account.auto_confirm_delivery,
      remark: account.remark,
      pause_time: account.pause_time,
    });
    setEditModalVisible(true);
  };

  // 打开默认回复管理
  const openDefaultReplyManager = async (account: Account) => {
    try {
      setCurrentAccount(account);
      const response = await accountApi.getDefaultReplies(account.id);
      if (response.success) {
        setDefaultReplies(response.data || []);
        setDefaultReplyModalVisible(true);
      }
    } catch (error: any) {
      message.error(error.message || '获取默认回复失败');
    }
  };

  // 更新默认回复
  const handleUpdateDefaultReplies = async () => {
    if (!currentAccount) return;

    try {
      const response = await accountApi.updateDefaultReplies(currentAccount.id, defaultReplies);
      if (response.success) {
        message.success('默认回复更新成功');
        setDefaultReplyModalVisible(false);
        setCurrentAccount(null);
        setDefaultReplies([]);
      }
    } catch (error: any) {
      message.error(error.message || '更新默认回复失败');
    }
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <UserOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              账号管理
            </Title>
            <Text type="secondary">管理闲鱼账号Cookie信息</Text>
          </div>
          <div className="header-extra">
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setAddModalVisible(true)}
              >
                添加账号
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadAccounts}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 账号列表 */}
      <Card
        title={
          <Space>
            <UserOutlined />
            <span>账号列表</span>
          </Space>
        }
        extra={
          <Text type="secondary">
            共 {accounts.length} 个账号
          </Text>
        }
      >
        <Table
          columns={[
            {
              title: '账号ID',
              dataIndex: 'id',
              key: 'id',
              width: '12%',
              render: (text: string) => (
                <Text code copyable={{ text }}>
                  {text}
                </Text>
              ),
            },
            {
              title: 'Cookie值',
              dataIndex: 'cookie_value',
              key: 'cookie_value',
              width: '20%',
              render: (text: string) => (
                <Text ellipsis={{ tooltip: text }} style={{ maxWidth: 200 }}>
                  {text}
                </Text>
              ),
            },
            {
              title: '关键词',
              dataIndex: 'keyword_count',
              key: 'keyword_count',
              width: '8%',
              render: (count: number) => (
                <Tag color={count > 0 ? 'success' : 'default'}>
                  {count}
                </Tag>
              ),
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              width: '8%',
              render: (status: boolean) => (
                <Tag color={status ? 'success' : 'default'}>
                  {status ? '启用' : '禁用'}
                </Tag>
              ),
            },
            {
              title: '默认回复',
              dataIndex: 'default_reply',
              key: 'default_reply',
              width: '8%',
              render: (enabled: boolean) => (
                <Tag color={enabled ? 'processing' : 'default'}>
                  {enabled ? '启用' : '禁用'}
                </Tag>
              ),
            },
            {
              title: 'AI回复',
              dataIndex: 'ai_reply',
              key: 'ai_reply',
              width: '8%',
              render: (enabled: boolean) => (
                <Tag color={enabled ? 'purple' : 'default'}>
                  {enabled ? '启用' : '禁用'}
                </Tag>
              ),
            },
            {
              title: '自动确认发货',
              dataIndex: 'auto_confirm_delivery',
              key: 'auto_confirm_delivery',
              width: '10%',
              render: (enabled: boolean) => (
                <Tag color={enabled ? 'orange' : 'default'}>
                  {enabled ? '启用' : '禁用'}
                </Tag>
              ),
            },
            {
              title: '备注',
              dataIndex: 'remark',
              key: 'remark',
              width: '12%',
              render: (text: string) => (
                <Text ellipsis={{ tooltip: text }} style={{ maxWidth: 120 }}>
                  {text || '-'}
                </Text>
              ),
            },
            {
              title: (
                <Space>
                  暂停时间
                  <Tooltip title="检测到手动发出消息后，自动回复暂停的时间长度（分钟）">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              ),
              dataIndex: 'pause_time',
              key: 'pause_time',
              width: '10%',
              render: (time: number) => `${time}分钟`,
            },
            {
              title: '操作',
              key: 'action',
              width: '14%',
              render: (_, record: Account) => (
                <Space size="small">
                  <Tooltip title="编辑账号">
                    <Button
                      type="text"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => openEditModal(record)}
                    />
                  </Tooltip>
                  <Tooltip title="默认回复管理">
                    <Button
                      type="text"
                      size="small"
                      icon={<SettingOutlined />}
                      onClick={() => openDefaultReplyManager(record)}
                    />
                  </Tooltip>
                  <Popconfirm
                    title="确定要删除这个账号吗？"
                    description="此操作不可恢复，将删除账号及其所有相关数据。"
                    onConfirm={() => handleDeleteAccount(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Tooltip title="删除账号">
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                      />
                    </Tooltip>
                  </Popconfirm>
                </Space>
              ),
            },
          ]}
          dataSource={accounts}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          }}
          scroll={{ x: 1200 }}
          locale={{
            emptyText: (
              <div style={{ padding: '40px 0', textAlign: 'center' }}>
                <UserOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                <div>
                  <Title level={4} type="secondary">暂无账号</Title>
                  <Text type="secondary">请添加新的闲鱼账号开始使用</Text>
                </div>
              </div>
            ),
          }}
        />
      </Card>

      {/* 添加账号模态框 */}
      <Modal
        title={
          <Space>
            <PlusOutlined />
            <span>添加新账号</span>
          </Space>
        }
        open={addModalVisible}
        onCancel={() => {
          setAddModalVisible(false);
          setShowManualInput(false);
          addForm.resetFields();
        }}
        footer={null}
        width={800}
      >
        <div style={{ marginBottom: 24 }}>
          <Alert
            message="选择添加方式"
            description="点击下方按钮选择您要使用的账号添加方式"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Row gutter={16}>
            <Col span={12}>
              <Card
                hoverable
                style={{ textAlign: 'center', cursor: 'pointer' }}
                onClick={() => message.info('扫码登录功能开发中...')}
              >
                <QrcodeOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 8 }} />
                <div style={{ fontWeight: 'bold' }}>扫码登录</div>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  不推荐，一般都不成功
                </Text>
              </Card>
            </Col>
            <Col span={12}>
              <Card
                hoverable
                style={{ textAlign: 'center', cursor: 'pointer' }}
                onClick={() => setShowManualInput(true)}
              >
                <KeyboardOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 8 }} />
                <div style={{ fontWeight: 'bold' }}>手动输入</div>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  推荐方式，使用消息界面的cookie
                </Text>
              </Card>
            </Col>
          </Row>
        </div>

        {showManualInput && (
          <div>
            <Alert
              message="提示"
              description="推荐使用扫码登录，更加安全便捷。如需手动输入，请确保Cookie信息的准确性。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Form
              form={addForm}
              layout="vertical"
              onFinish={handleAddAccount}
            >
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="账号ID"
                    name="id"
                    rules={[
                      { required: true, message: '请输入账号ID' },
                      { pattern: /^[a-zA-Z0-9_-]+$/, message: '账号ID只能包含字母、数字、下划线和横线' }
                    ]}
                  >
                    <Input placeholder="唯一标识" />
                  </Form.Item>
                </Col>
                <Col span={16}>
                  <Form.Item
                    label="Cookie值"
                    name="cookie_value"
                    rules={[{ required: true, message: '请输入Cookie值' }]}
                  >
                    <Input placeholder="完整Cookie字符串" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                <Space>
                  <Button onClick={() => setShowManualInput(false)}>
                    取消
                  </Button>
                  <Button type="primary" htmlType="submit">
                    添加账号
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* 编辑账号模态框 */}
      <Modal
        title={
          <Space>
            <EditOutlined />
            <span>编辑账号设置</span>
          </Space>
        }
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setCurrentAccount(null);
          editForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleUpdateAccount}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="账号状态"
                name="status"
                valuePropName="checked"
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="默认回复"
                name="default_reply"
                valuePropName="checked"
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="AI回复"
                name="ai_reply"
                valuePropName="checked"
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="自动确认发货"
                name="auto_confirm_delivery"
                valuePropName="checked"
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label={
              <Space>
                暂停时间（分钟）
                <Tooltip title="检测到手动发出消息后，自动回复暂停的时间长度">
                  <InfoCircleOutlined />
                </Tooltip>
              </Space>
            }
            name="pause_time"
            rules={[{ required: true, message: '请输入暂停时间' }]}
          >
            <InputNumber
              min={0}
              max={1440}
              style={{ width: '100%' }}
              placeholder="暂停时间（分钟）"
            />
          </Form.Item>

          <Form.Item
            label="备注"
            name="remark"
          >
            <TextArea
              rows={3}
              placeholder="账号备注信息"
              maxLength={200}
              showCount
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setEditModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 默认回复管理模态框 */}
      <Modal
        title={
          <Space>
            <SettingOutlined />
            <span>默认回复管理</span>
            {currentAccount && (
              <Tag color="blue">{currentAccount.id}</Tag>
            )}
          </Space>
        }
        open={defaultReplyModalVisible}
        onCancel={() => {
          setDefaultReplyModalVisible(false);
          setCurrentAccount(null);
          setDefaultReplies([]);
        }}
        footer={
          <Space>
            <Button onClick={() => setDefaultReplyModalVisible(false)}>
              取消
            </Button>
            <Button type="primary" onClick={handleUpdateDefaultReplies}>
              保存
            </Button>
          </Space>
        }
        width={700}
      >
        <div>
          <Alert
            message="默认回复设置"
            description="设置该账号的默认回复内容，当没有匹配到关键词时将使用这些回复。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <div style={{ marginBottom: 16 }}>
            <Button
              type="dashed"
              onClick={() => setDefaultReplies([...defaultReplies, ''])}
              style={{ width: '100%' }}
            >
              <PlusOutlined /> 添加回复
            </Button>
          </div>

          <Space direction="vertical" style={{ width: '100%' }}>
            {defaultReplies.map((reply, index) => (
              <div key={index} style={{ display: 'flex', gap: 8 }}>
                <TextArea
                  value={reply}
                  onChange={(e) => {
                    const newReplies = [...defaultReplies];
                    newReplies[index] = e.target.value;
                    setDefaultReplies(newReplies);
                  }}
                  placeholder={`默认回复 ${index + 1}`}
                  rows={2}
                  style={{ flex: 1 }}
                />
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => {
                    const newReplies = defaultReplies.filter((_, i) => i !== index);
                    setDefaultReplies(newReplies);
                  }}
                />
              </div>
            ))}
          </Space>

          {defaultReplies.length === 0 && (
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              <Text type="secondary">暂无默认回复，点击上方按钮添加</Text>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default AccountManagement;
