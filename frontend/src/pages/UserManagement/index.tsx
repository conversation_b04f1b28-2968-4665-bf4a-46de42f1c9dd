import React from 'react';
import { Card, Typography } from 'antd';
import { TeamOutlined } from '@ant-design/icons';

const { Title } = Typography;

const UserManagement: React.FC = () => {
  return (
    <div>
      <div className="page-header">
        <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
          <TeamOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          用户管理
        </Title>
      </div>
      <Card>
        <p>用户管理功能正在开发中...</p>
      </Card>
    </div>
  );
};

export default UserManagement;
