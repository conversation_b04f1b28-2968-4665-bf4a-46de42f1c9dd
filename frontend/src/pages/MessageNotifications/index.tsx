import React from 'react';
import { Card, Typography } from 'antd';
import { CommentOutlined } from '@ant-design/icons';

const { Title } = Typography;

const MessageNotifications: React.FC = () => {
  return (
    <div>
      <div className="page-header">
        <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
          <CommentOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          消息通知
        </Title>
      </div>
      <Card>
        <p>消息通知功能正在开发中...</p>
      </Card>
    </div>
  );
};

export default MessageNotifications;
