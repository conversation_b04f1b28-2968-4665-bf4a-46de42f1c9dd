import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Table,
  Switch,
  Space,
  Tag,
  message,
  Row,
  Col,
  Al<PERSON>,
  Button,
} from 'antd';
import {
  CommentOutlined,
  ReloadOutlined,
  UserOutlined,
  BellOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import { notificationApi, accountApi } from '../../services/api';
import type { MessageNotification, Account, NotificationChannel } from '../../types';

const { Title, Text } = Typography;

const MessageNotifications: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [notifications, setNotifications] = useState<MessageNotification[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [channels, setChannels] = useState<NotificationChannel[]>([]);

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);
      const [notificationsRes, accountsRes, channelsRes] = await Promise.all([
        notificationApi.getMessageNotifications(),
        accountApi.getAccounts(),
        notificationApi.getChannels(),
      ]);

      if (notificationsRes.success) {
        setNotifications(notificationsRes.data || []);
      }
      if (accountsRes.success) {
        setAccounts(accountsRes.data || []);
      }
      if (channelsRes.success) {
        setChannels(channelsRes.data || []);
      }
    } catch (error: any) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新通知配置
  const handleUpdateNotification = async (cookieId: string, channelId: string, enabled: boolean) => {
    try {
      const response = await notificationApi.updateMessageNotification(cookieId, channelId, enabled);
      if (response.success) {
        message.success(`通知配置${enabled ? '启用' : '禁用'}成功`);
        loadData();
      }
    } catch (error: any) {
      message.error(error.message || '更新通知配置失败');
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // 构建表格数据
  const tableData = accounts.map(account => {
    const accountNotifications = notifications.filter(n => n.cookie_id === account.id);
    return {
      ...account,
      notifications: accountNotifications,
    };
  });

  // 获取通知渠道配置
  const getChannelConfig = (accountId: string, channelId: string) => {
    return notifications.find(n => n.cookie_id === accountId && n.channel_id === channelId);
  };

  // 统计数据
  const stats = {
    totalAccounts: accounts.length,
    totalChannels: channels.length,
    activeNotifications: notifications.filter(n => n.status).length,
    totalNotifications: notifications.length,
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <CommentOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              消息通知配置
            </Title>
            <Text type="secondary">为每个账号配置不同的通知渠道</Text>
          </div>
          <div className="header-extra">
            <Button
              icon={<ReloadOutlined />}
              onClick={loadData}
              loading={loading}
            >
              刷新
            </Button>
          </div>
        </div>
      </div>

      {/* 统计信息 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <UserOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                {stats.totalAccounts}
              </div>
              <div style={{ color: '#666' }}>总账号数</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <BellOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                {stats.totalChannels}
              </div>
              <div style={{ color: '#666' }}>通知渠道</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <CheckCircleOutlined style={{ fontSize: 24, color: '#faad14', marginBottom: 8 }} />
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>
                {stats.activeNotifications}
              </div>
              <div style={{ color: '#666' }}>启用配置</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <CommentOutlined style={{ fontSize: 24, color: '#722ed1', marginBottom: 8 }} />
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#722ed1' }}>
                {stats.totalNotifications}
              </div>
              <div style={{ color: '#666' }}>总配置数</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 通知配置表格 */}
      <Card
        title={
          <Space>
            <CommentOutlined />
            <span>账号通知配置</span>
          </Space>
        }
      >
        <Alert
          message="通知配置说明"
          description="为每个账号配置不同的通知渠道。当账号收到新消息或发生重要事件时，系统会通过启用的渠道发送通知。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Table
          columns={[
            {
              title: '账号ID',
              dataIndex: 'id',
              key: 'id',
              width: '15%',
              render: (text: string, record: any) => (
                <Space direction="vertical" size="small">
                  <Text code>{text}</Text>
                  <Tag color={record.status ? 'success' : 'default'} size="small">
                    {record.status ? '启用' : '禁用'}
                  </Tag>
                </Space>
              ),
            },
            {
              title: '关键词数',
              dataIndex: 'keyword_count',
              key: 'keyword_count',
              width: '10%',
              render: (count: number) => (
                <Tag color="blue">{count}</Tag>
              ),
            },
            ...channels.map(channel => ({
              title: (
                <Space direction="vertical" size="small" style={{ textAlign: 'center' }}>
                  <Text strong>{channel.name}</Text>
                  <Tag color="blue" size="small">{channel.type}</Tag>
                </Space>
              ),
              key: channel.id,
              width: `${Math.max(15, 70 / channels.length)}%`,
              render: (_: any, record: any) => {
                const config = getChannelConfig(record.id, channel.id);
                const isEnabled = config?.status || false;

                return (
                  <div style={{ textAlign: 'center' }}>
                    <Switch
                      checked={isEnabled}
                      onChange={(checked) => handleUpdateNotification(record.id, channel.id, checked)}
                      checkedChildren={<CheckCircleOutlined />}
                      unCheckedChildren={<CloseCircleOutlined />}
                    />
                    <div style={{ marginTop: 4 }}>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {isEnabled ? '已启用' : '已禁用'}
                      </Text>
                    </div>
                  </div>
                );
              },
            })),
          ]}
          dataSource={tableData}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          }}
          scroll={{ x: 800 }}
          locale={{
            emptyText: (
              <div style={{ padding: '40px 0', textAlign: 'center' }}>
                <CommentOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                <div>
                  <Title level={4} type="secondary">暂无账号数据</Title>
                  <Text type="secondary">请先添加账号和通知渠道</Text>
                </div>
              </div>
            ),
          }}
        />
      </Card>
    </div>
  );
};

export default MessageNotifications;
