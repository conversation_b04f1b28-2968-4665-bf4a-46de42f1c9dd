import React from 'react';
import { Card, Typography } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

const { Title } = Typography;

const ItemSearch: React.FC = () => {
  return (
    <div>
      <div className="page-header">
        <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
          <SearchOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          商品搜索
        </Title>
      </div>
      <Card>
        <p>商品搜索功能正在开发中...</p>
      </Card>
    </div>
  );
};

export default ItemSearch;
