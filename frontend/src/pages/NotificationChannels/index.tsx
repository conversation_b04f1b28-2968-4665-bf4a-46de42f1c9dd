import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Table,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Row,
  Col,
  Alert,
  Tabs,
} from 'antd';
import {
  BellOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  QqOutlined,
  WechatOutlined,
  MailOutlined,
  ApiOutlined,
  PhoneOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import { notificationApi } from '../../services/api';
import type { NotificationChannel } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

const NotificationChannels: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [channels, setChannels] = useState<NotificationChannel[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingChannel, setEditingChannel] = useState<NotificationChannel | null>(null);
  const [testLoading, setTestLoading] = useState<string>('');
  const [form] = Form.useForm();

  // 加载通知渠道列表
  const loadChannels = async () => {
    try {
      setLoading(true);
      const response = await notificationApi.getChannels();
      if (response.success) {
        setChannels(response.data || []);
      }
    } catch (error: any) {
      console.error('加载通知渠道失败:', error);
      message.error('加载通知渠道失败');
    } finally {
      setLoading(false);
    }
  };

  // 添加通知渠道
  const handleAddChannel = async (values: any) => {
    try {
      const response = await notificationApi.addChannel(values);
      if (response.success) {
        message.success('通知渠道添加成功');
        setModalVisible(false);
        form.resetFields();
        loadChannels();
      }
    } catch (error: any) {
      message.error(error.message || '添加通知渠道失败');
    }
  };

  // 编辑通知渠道
  const handleEditChannel = async (values: any) => {
    if (!editingChannel) return;

    try {
      const response = await notificationApi.updateChannel(editingChannel.id, values);
      if (response.success) {
        message.success('通知渠道更新成功');
        setModalVisible(false);
        setEditingChannel(null);
        form.resetFields();
        loadChannels();
      }
    } catch (error: any) {
      message.error(error.message || '更新通知渠道失败');
    }
  };

  // 删除通知渠道
  const handleDeleteChannel = async (id: string) => {
    try {
      const response = await notificationApi.deleteChannel(id);
      if (response.success) {
        message.success('通知渠道删除成功');
        loadChannels();
      }
    } catch (error: any) {
      message.error(error.message || '删除通知渠道失败');
    }
  };

  // 测试通知渠道
  const handleTestChannel = async (id: string) => {
    try {
      setTestLoading(id);
      const response = await notificationApi.testChannel(id);
      if (response.success) {
        message.success('测试消息发送成功');
      }
    } catch (error: any) {
      message.error(error.message || '测试消息发送失败');
    } finally {
      setTestLoading('');
    }
  };

  // 打开编辑模态框
  const openEditModal = (channel: NotificationChannel) => {
    setEditingChannel(channel);
    form.setFieldsValue({
      ...channel,
      config: channel.config,
    });
    setModalVisible(true);
  };

  useEffect(() => {
    loadChannels();
  }, []);

  // 通知类型配置
  const typeConfig = {
    qq: { color: 'blue', text: 'QQ', icon: <QqOutlined /> },
    dingtalk: { color: 'cyan', text: '钉钉', icon: <RobotOutlined /> },
    feishu: { color: 'green', text: '飞书', icon: <RobotOutlined /> },
    bark: { color: 'orange', text: 'Bark', icon: <PhoneOutlined /> },
    email: { color: 'red', text: '邮箱', icon: <MailOutlined /> },
    webhook: { color: 'purple', text: 'Webhook', icon: <ApiOutlined /> },
    wechat: { color: 'green', text: '微信', icon: <WechatOutlined /> },
    telegram: { color: 'blue', text: 'Telegram', icon: <RobotOutlined /> },
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <BellOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              通知渠道管理
            </Title>
            <Text type="secondary">配置各种消息通知渠道，支持QQ、钉钉、邮箱等多种方式</Text>
          </div>
          <div className="header-extra">
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setModalVisible(true)}
              >
                添加渠道
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadChannels}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 通知渠道列表 */}
      <Card
        title={
          <Space>
            <BellOutlined />
            <span>通知渠道列表</span>
          </Space>
        }
      >
        <Table
          columns={[
            {
              title: '渠道名称',
              dataIndex: 'name',
              key: 'name',
              width: '20%',
            },
            {
              title: '类型',
              dataIndex: 'type',
              key: 'type',
              width: '12%',
              render: (type: NotificationChannel['type']) => {
                const config = typeConfig[type];
                return (
                  <Tag color={config.color} icon={config.icon}>
                    {config.text}
                  </Tag>
                );
              },
            },
            {
              title: '配置信息',
              dataIndex: 'config',
              key: 'config',
              width: '30%',
              render: (config: Record<string, any>, record: NotificationChannel) => {
                const displayConfig = { ...config };
                // 隐藏敏感信息
                if (displayConfig.token) displayConfig.token = '***';
                if (displayConfig.password) displayConfig.password = '***';
                if (displayConfig.secret) displayConfig.secret = '***';

                return (
                  <Text ellipsis={{ tooltip: JSON.stringify(displayConfig, null, 2) }}>
                    {Object.entries(displayConfig).slice(0, 2).map(([key, value]) =>
                      `${key}: ${value}`
                    ).join(', ')}
                  </Text>
                );
              },
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              width: '8%',
              render: (status: boolean) => (
                <Tag color={status ? 'success' : 'default'}>
                  {status ? '启用' : '禁用'}
                </Tag>
              ),
            },
            {
              title: '创建时间',
              dataIndex: 'created_at',
              key: 'created_at',
              width: '15%',
              render: (time: string) => (
                <Text type="secondary">
                  {dayjs(time).format('MM-DD HH:mm')}
                </Text>
              ),
            },
            {
              title: '操作',
              key: 'action',
              width: '15%',
              render: (_, record: NotificationChannel) => (
                <Space size="small">
                  <Button
                    type="text"
                    size="small"
                    icon={<InfoCircleOutlined />}
                    onClick={() => handleTestChannel(record.id)}
                    loading={testLoading === record.id}
                    title="测试通知"
                  />
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => openEditModal(record)}
                  />
                  <Popconfirm
                    title="确定要删除这个通知渠道吗？"
                    onConfirm={() => handleDeleteChannel(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                    />
                  </Popconfirm>
                </Space>
              ),
            },
          ]}
          dataSource={channels}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          }}
          locale={{
            emptyText: (
              <div style={{ padding: '40px 0', textAlign: 'center' }}>
                <BellOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                <div>
                  <Title level={4} type="secondary">暂无通知渠道</Title>
                  <Text type="secondary">点击"添加渠道"开始配置消息通知</Text>
                </div>
              </div>
            ),
          }}
        />
      </Card>

      {/* 添加/编辑通知渠道模态框 */}
      <Modal
        title={
          <Space>
            {editingChannel ? <EditOutlined /> : <PlusOutlined />}
            <span>{editingChannel ? '编辑通知渠道' : '添加通知渠道'}</span>
          </Space>
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingChannel(null);
          form.resetFields();
        }}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={editingChannel ? handleEditChannel : handleAddChannel}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="渠道名称"
                name="name"
                rules={[{ required: true, message: '请输入渠道名称' }]}
              >
                <Input placeholder="例如：主要通知群" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="通知类型"
                name="type"
                rules={[{ required: true, message: '请选择通知类型' }]}
              >
                <Select placeholder="选择通知类型">
                  <Option value="qq">
                    <Space><QqOutlined />QQ</Space>
                  </Option>
                  <Option value="dingtalk">
                    <Space><RobotOutlined />钉钉</Space>
                  </Option>
                  <Option value="feishu">
                    <Space><RobotOutlined />飞书</Space>
                  </Option>
                  <Option value="bark">
                    <Space><PhoneOutlined />Bark</Space>
                  </Option>
                  <Option value="email">
                    <Space><MailOutlined />邮箱</Space>
                  </Option>
                  <Option value="webhook">
                    <Space><ApiOutlined />Webhook</Space>
                  </Option>
                  <Option value="wechat">
                    <Space><WechatOutlined />微信</Space>
                  </Option>
                  <Option value="telegram">
                    <Space><RobotOutlined />Telegram</Space>
                  </Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="渠道状态"
            name="status"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) => {
              const type = getFieldValue('type');

              if (!type) return null;

              return (
                <Card title="配置参数" size="small" style={{ marginBottom: 16 }}>
                  {type === 'qq' && (
                    <>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            label="QQ号"
                            name={['config', 'qq']}
                            rules={[{ required: true, message: '请输入QQ号' }]}
                          >
                            <Input placeholder="QQ号码" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            label="授权码"
                            name={['config', 'token']}
                            rules={[{ required: true, message: '请输入授权码' }]}
                          >
                            <Input.Password placeholder="QQ授权码" />
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  )}

                  {type === 'dingtalk' && (
                    <>
                      <Form.Item
                        label="Webhook地址"
                        name={['config', 'webhook']}
                        rules={[{ required: true, message: '请输入Webhook地址' }]}
                      >
                        <Input placeholder="https://oapi.dingtalk.com/robot/send?access_token=..." />
                      </Form.Item>
                      <Form.Item
                        label="密钥"
                        name={['config', 'secret']}
                      >
                        <Input.Password placeholder="钉钉机器人密钥（可选）" />
                      </Form.Item>
                    </>
                  )}

                  {type === 'feishu' && (
                    <>
                      <Form.Item
                        label="Webhook地址"
                        name={['config', 'webhook']}
                        rules={[{ required: true, message: '请输入Webhook地址' }]}
                      >
                        <Input placeholder="https://open.feishu.cn/open-apis/bot/v2/hook/..." />
                      </Form.Item>
                      <Form.Item
                        label="密钥"
                        name={['config', 'secret']}
                      >
                        <Input.Password placeholder="飞书机器人密钥（可选）" />
                      </Form.Item>
                    </>
                  )}

                  {type === 'bark' && (
                    <>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            label="设备Key"
                            name={['config', 'key']}
                            rules={[{ required: true, message: '请输入设备Key' }]}
                          >
                            <Input placeholder="Bark设备Key" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            label="服务器地址"
                            name={['config', 'server']}
                          >
                            <Input placeholder="https://api.day.app（可选）" />
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  )}

                  {type === 'email' && (
                    <>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            label="SMTP服务器"
                            name={['config', 'smtp_server']}
                            rules={[{ required: true, message: '请输入SMTP服务器' }]}
                          >
                            <Input placeholder="smtp.gmail.com" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            label="端口"
                            name={['config', 'port']}
                            rules={[{ required: true, message: '请输入端口' }]}
                          >
                            <Input placeholder="587" />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            label="发件邮箱"
                            name={['config', 'from_email']}
                            rules={[{ required: true, message: '请输入发件邮箱' }]}
                          >
                            <Input placeholder="<EMAIL>" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            label="收件邮箱"
                            name={['config', 'to_email']}
                            rules={[{ required: true, message: '请输入收件邮箱' }]}
                          >
                            <Input placeholder="<EMAIL>" />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Form.Item
                        label="邮箱密码"
                        name={['config', 'password']}
                        rules={[{ required: true, message: '请输入邮箱密码' }]}
                      >
                        <Input.Password placeholder="邮箱密码或应用专用密码" />
                      </Form.Item>
                    </>
                  )}

                  {type === 'webhook' && (
                    <>
                      <Form.Item
                        label="Webhook地址"
                        name={['config', 'url']}
                        rules={[{ required: true, message: '请输入Webhook地址' }]}
                      >
                        <Input placeholder="https://your-webhook-url.com/notify" />
                      </Form.Item>
                      <Form.Item
                        label="请求方法"
                        name={['config', 'method']}
                      >
                        <Select placeholder="POST" defaultValue="POST">
                          <Option value="POST">POST</Option>
                          <Option value="GET">GET</Option>
                        </Select>
                      </Form.Item>
                      <Form.Item
                        label="请求头"
                        name={['config', 'headers']}
                      >
                        <TextArea
                          rows={3}
                          placeholder='{"Content-Type": "application/json"}'
                        />
                      </Form.Item>
                    </>
                  )}

                  {type === 'telegram' && (
                    <>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            label="Bot Token"
                            name={['config', 'token']}
                            rules={[{ required: true, message: '请输入Bot Token' }]}
                          >
                            <Input.Password placeholder="Bot Token" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            label="Chat ID"
                            name={['config', 'chat_id']}
                            rules={[{ required: true, message: '请输入Chat ID' }]}
                          >
                            <Input placeholder="Chat ID" />
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  )}
                </Card>
              );
            }}
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingChannel ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default NotificationChannels;
