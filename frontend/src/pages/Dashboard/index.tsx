import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Space,
  Typography,
  Badge,
  Spin,
  message,
} from 'antd';
import {
  UserOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { dashboardApi } from '../../services/api';
import type { DashboardStats } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface AccountDetail {
  id: string;
  keyword_count: number;
  status: boolean;
  updated_at: string;
}

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalAccounts: 0,
    totalKeywords: 0,
    activeAccounts: 0,
    totalOrders: 0,
    accounts: [],
  });
  const [systemVersion, setSystemVersion] = useState('加载中...');

  // 加载仪表盘数据
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [dashboardResponse, versionResponse] = await Promise.all([
        dashboardApi.getDashboardData(),
        dashboardApi.getSystemVersion(),
      ]);

      if (dashboardResponse.success) {
        setStats(dashboardResponse.data);
      }

      if (versionResponse.success) {
        setSystemVersion(versionResponse.data.version);
      }
    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
      message.error('加载仪表盘数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  // 账号详情表格列配置
  const columns = [
    {
      title: '账号ID',
      dataIndex: 'id',
      key: 'id',
      width: '25%',
      render: (text: string) => (
        <Text code copyable={{ text }}>
          {text}
        </Text>
      ),
    },
    {
      title: '关键词数量',
      dataIndex: 'keyword_count',
      key: 'keyword_count',
      width: '20%',
      render: (count: number) => (
        <Badge
          count={count}
          showZero
          color={count > 0 ? '#52c41a' : '#d9d9d9'}
          style={{ backgroundColor: count > 0 ? '#52c41a' : '#d9d9d9' }}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '15%',
      render: (status: boolean) => (
        <Tag color={status ? 'success' : 'default'} icon={status ? <CheckCircleOutlined /> : undefined}>
          {status ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '最后更新',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: '40%',
      render: (time: string) => (
        <Text type="secondary">
          {dayjs(time).format('YYYY-MM-DD HH:mm:ss')}
        </Text>
      ),
    },
  ];

  return (
    <div className="dashboard-container">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <UserOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              仪表盘
            </Title>
            <Text type="secondary">系统概览和统计信息</Text>
          </div>
          <div className="header-extra">
            <Space>
              <Badge color="blue" text={`版本: ${systemVersion}`} />
              <ReloadOutlined
                style={{ cursor: 'pointer', fontSize: 16 }}
                onClick={loadDashboardData}
                spin={loading}
              />
            </Space>
          </div>
        </div>
      </div>

      <Spin spinning={loading}>
        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总账号数"
                value={stats.totalAccounts}
                prefix={<UserOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总关键词数"
                value={stats.totalKeywords}
                prefix={<MessageOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="启用账号数"
                value={stats.activeAccounts}
                prefix={<CheckCircleOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总订单数"
                value={stats.totalOrders}
                prefix={<FileTextOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 账号详情列表 */}
        <Card
          title={
            <Space>
              <UserOutlined />
              <span>账号详情</span>
            </Space>
          }
          extra={
            <Text type="secondary">
              共 {stats.accounts.length} 个账号
            </Text>
          }
        >
          <Table
            columns={columns}
            dataSource={stats.accounts}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            }}
            size="middle"
            scroll={{ x: 800 }}
          />
        </Card>
      </Spin>
    </div>
  );
};

export default Dashboard;
