import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Select,
  Input,
  Space,
  message,
  Modal,
  Form,
  Upload,
  Row,
  Col,
  Tag,
  Tooltip,
  Badge,
  Alert,
  Popconfirm,
  Spin,
} from 'antd';
import {
  MessageOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  DownloadOutlined,
  UploadOutlined,
  FileImageOutlined,
  InfoCircleOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { keywordApi, accountApi, itemApi } from '../../services/api';
import type { Keyword, Account, Item } from '../../types';
import './AutoReply.css';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface KeywordWithItem extends Keyword {
  item_title?: string;
}

const AutoReply: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [keywords, setKeywords] = useState<KeywordWithItem[]>([]);
  const [items, setItems] = useState<Item[]>([]);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [editingKeyword, setEditingKeyword] = useState<KeywordWithItem | null>(null);

  const [form] = Form.useForm();
  const [imageForm] = Form.useForm();

  // 加载账号列表
  const loadAccounts = async () => {
    try {
      const response = await accountApi.getAccounts();
      if (response.success) {
        setAccounts(response.data || []);
      }
    } catch (error) {
      console.error('加载账号列表失败:', error);
    }
  };

  // 加载关键词列表
  const loadKeywords = async (accountId: string) => {
    if (!accountId) return;

    try {
      setLoading(true);
      const response = await keywordApi.getKeywords(accountId);
      if (response.success) {
        setKeywords(response.data || []);
      }
    } catch (error: any) {
      console.error('加载关键词失败:', error);
      message.error('加载关键词失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载商品列表
  const loadItems = async (accountId: string) => {
    if (!accountId) return;

    try {
      const response = await itemApi.getItemsByAccount(accountId);
      if (response.success) {
        setItems(response.data?.items || []);
      }
    } catch (error) {
      console.error('加载商品列表失败:', error);
    }
  };

  // 账号选择变化
  const handleAccountChange = (accountId: string) => {
    setSelectedAccount(accountId);
    if (accountId) {
      loadKeywords(accountId);
      loadItems(accountId);
    } else {
      setKeywords([]);
      setItems([]);
    }
  };

  // 添加关键词
  const handleAddKeyword = async (values: any) => {
    try {
      const keywordData = {
        cookie_id: selectedAccount,
        keyword: values.keyword,
        reply: values.reply || '',
        item_id: values.item_id || '',
        type: 'text' as const,
      };

      const response = await keywordApi.addKeyword(keywordData);
      if (response.success) {
        message.success('关键词添加成功');
        setAddModalVisible(false);
        form.resetFields();
        loadKeywords(selectedAccount);
      }
    } catch (error: any) {
      message.error(error.message || '添加关键词失败');
    }
  };

  // 编辑关键词
  const handleEditKeyword = async (values: any) => {
    if (!editingKeyword) return;

    try {
      const response = await keywordApi.updateKeyword(editingKeyword.id, values);
      if (response.success) {
        message.success('关键词更新成功');
        setAddModalVisible(false);
        setEditingKeyword(null);
        form.resetFields();
        loadKeywords(selectedAccount);
      }
    } catch (error: any) {
      message.error(error.message || '更新关键词失败');
    }
  };

  // 删除关键词
  const handleDeleteKeyword = async (id: string) => {
    try {
      const response = await keywordApi.deleteKeyword(id);
      if (response.success) {
        message.success('关键词删除成功');
        loadKeywords(selectedAccount);
      }
    } catch (error: any) {
      message.error(error.message || '删除关键词失败');
    }
  };

  // 打开编辑模态框
  const openEditModal = (keyword: KeywordWithItem) => {
    setEditingKeyword(keyword);
    form.setFieldsValue({
      keyword: keyword.keyword,
      reply: keyword.reply,
      item_id: keyword.item_id,
    });
    setAddModalVisible(true);
  };

  // 导出关键词
  const handleExportKeywords = async () => {
    if (!selectedAccount) {
      message.warning('请先选择账号');
      return;
    }

    try {
      const response = await keywordApi.exportKeywords(selectedAccount);
      if (response.success) {
        // 创建下载链接
        const blob = new Blob([JSON.stringify(response.data, null, 2)], {
          type: 'application/json',
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `keywords_${selectedAccount}_${new Date().toISOString().slice(0, 10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        message.success('关键词导出成功');
      }
    } catch (error: any) {
      message.error(error.message || '导出关键词失败');
    }
  };

  useEffect(() => {
    loadAccounts();
  }, []);

  // 获取当前账号信息
  const currentAccount = accounts.find(acc => acc.id === selectedAccount);

  return (
    <div className="auto-reply-container">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <MessageOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              自动回复
            </Title>
            <Text type="secondary">设置账号的关键词自动回复</Text>
          </div>
        </div>
      </div>

      {/* 账号选择器 */}
      <Card className="account-selector-card" style={{ marginBottom: 24 }}>
        <div className="account-selector-header">
          <div className="selector-icon">
            <UserOutlined />
          </div>
          <div>
            <Title level={4} style={{ margin: 0 }}>选择账号</Title>
            <Text type="secondary">选择要配置自动回复的闲鱼账号</Text>
          </div>
        </div>

        <Row gutter={[16, 16]} align="middle" style={{ marginTop: 16 }}>
          <Col xs={24} md={18}>
            <Select
              style={{ width: '100%' }}
              placeholder="🔍 请选择一个账号开始配置..."
              value={selectedAccount || undefined}
              onChange={handleAccountChange}
              size="large"
            >
              {accounts.map(account => (
                <Option key={account.id} value={account.id}>
                  <Space>
                    <span>{account.id}</span>
                    <Tag color={account.status ? 'success' : 'default'} size="small">
                      {account.status ? '启用' : '禁用'}
                    </Tag>
                    <Badge count={account.keyword_count} showZero color="#52c41a" />
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} md={6}>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={loadAccounts}
              style={{ width: '100%' }}
              size="large"
            >
              刷新列表
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 关键词管理区域 */}
      {selectedAccount && (
        <Card
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Space>
                <MessageOutlined />
                <span>关键词管理</span>
                {currentAccount && (
                  <Tag color={currentAccount.status ? 'success' : 'warning'}>
                    {currentAccount.status ? '🟢 启用' : '🔴 禁用'}
                    {!currentAccount.status && ' (配置的关键词不会参与自动回复)'}
                  </Tag>
                )}
              </Space>
              <Space>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExportKeywords}
                  size="small"
                >
                  导出
                </Button>
                <Button
                  icon={<UploadOutlined />}
                  onClick={() => setImportModalVisible(true)}
                  size="small"
                >
                  导入
                </Button>
              </Space>
            </div>
          }
        >
          {/* 添加关键词区域 */}
          <div className="keyword-input-area" style={{ marginBottom: 24 }}>
            <Alert
              message="支持变量"
              description={
                <div>
                  <Text code>{'{send_user_name}'}</Text> 用户昵称，
                  <Text code>{'{send_user_id}'}</Text> 用户ID，
                  <Text code>{'{send_message}'}</Text> 用户消息
                  <br />
                  <Text type="secondary">
                    <InfoCircleOutlined /> 回复内容留空时，匹配到关键词但不会自动回复，可用于屏蔽特定消息
                  </Text>
                </div>
              }
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <Input
                  placeholder="例如：你好"
                  value={form.getFieldValue('keyword')}
                  onChange={(e) => form.setFieldValue('keyword', e.target.value)}
                  onPressEnter={() => form.getFieldValue('reply') ? handleAddKeyword(form.getFieldsValue()) : form.getFieldInstance('reply')?.focus()}
                />
                <Text type="secondary" style={{ fontSize: 12 }}>关键词</Text>
              </Col>
              <Col xs={24} sm={8}>
                <Input
                  placeholder="例如：您好，欢迎咨询！留空表示不回复"
                  value={form.getFieldValue('reply')}
                  onChange={(e) => form.setFieldValue('reply', e.target.value)}
                  onPressEnter={() => handleAddKeyword(form.getFieldsValue())}
                />
                <Text type="secondary" style={{ fontSize: 12 }}>自动回复内容（可选）</Text>
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  style={{ width: '100%' }}
                  placeholder="选择商品或留空表示通用关键词"
                  value={form.getFieldValue('item_id')}
                  onChange={(value) => form.setFieldValue('item_id', value)}
                  allowClear
                >
                  {items.map(item => (
                    <Option key={item.item_id} value={item.item_id}>
                      <Tooltip title={item.title}>
                        <Text ellipsis style={{ maxWidth: 200 }}>
                          {item.title}
                        </Text>
                      </Tooltip>
                    </Option>
                  ))}
                </Select>
                <Text type="secondary" style={{ fontSize: 12 }}>商品ID（可选）</Text>
              </Col>
            </Row>

            <div style={{ marginTop: 16 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    const values = form.getFieldsValue();
                    if (!values.keyword?.trim()) {
                      message.warning('请填写关键词');
                      return;
                    }
                    handleAddKeyword(values);
                  }}
                >
                  添加文本关键词
                </Button>
                <Button
                  icon={<ImageOutlined />}
                  onClick={() => setImageModalVisible(true)}
                >
                  添加图片关键词
                </Button>
              </Space>
            </div>
          </div>

          {/* 关键词列表 */}
          <Spin spinning={loading}>
            <div className="keywords-list">
              {keywords.length === 0 ? (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <MessageOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                  <div>
                    <Title level={4} type="secondary">暂无关键词</Title>
                    <Text type="secondary">请添加关键词开始配置自动回复</Text>
                  </div>
                </div>
              ) : (
                <Row gutter={[16, 16]}>
                  {keywords.map((keyword, index) => (
                    <Col xs={24} sm={12} lg={8} xl={6} key={keyword.id}>
                      <Card
                        size="small"
                        className="keyword-card"
                        title={
                          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                            <Tag color={keyword.type === 'image' ? 'purple' : 'blue'}>
                              {keyword.type === 'image' ? '图片' : '文本'}
                            </Tag>
                            <Text strong ellipsis style={{ flex: 1 }}>
                              {keyword.keyword}
                            </Text>
                          </div>
                        }
                        extra={
                          <Space size="small">
                            {keyword.type !== 'image' && (
                              <Tooltip title="编辑">
                                <Button
                                  type="text"
                                  size="small"
                                  icon={<EditOutlined />}
                                  onClick={() => openEditModal(keyword)}
                                />
                              </Tooltip>
                            )}
                            <Popconfirm
                              title="确定要删除这个关键词吗？"
                              onConfirm={() => handleDeleteKeyword(keyword.id)}
                              okText="确定"
                              cancelText="取消"
                            >
                              <Tooltip title="删除">
                                <Button
                                  type="text"
                                  size="small"
                                  danger
                                  icon={<DeleteOutlined />}
                                />
                              </Tooltip>
                            </Popconfirm>
                          </Space>
                        }
                      >
                        <div style={{ marginBottom: 8 }}>
                          {keyword.item_id && (
                            <Tag color="orange" size="small">
                              商品: {keyword.item_id}
                            </Tag>
                          )}
                          {!keyword.item_id && (
                            <Tag color="default" size="small">
                              通用关键词
                            </Tag>
                          )}
                        </div>

                        {keyword.type === 'image' ? (
                          <div style={{ textAlign: 'center' }}>
                            {keyword.image_path && (
                              <img
                                src={keyword.image_path}
                                alt="关键词图片"
                                style={{ maxWidth: '100%', maxHeight: 100, cursor: 'pointer' }}
                                onClick={() => {
                                  Modal.info({
                                    title: '关键词图片',
                                    content: (
                                      <img
                                        src={keyword.image_path}
                                        alt="关键词图片"
                                        style={{ width: '100%' }}
                                      />
                                    ),
                                    width: 600,
                                  });
                                }}
                              />
                            )}
                          </div>
                        ) : (
                          <div>
                            <Text type="secondary" style={{ fontSize: 12 }}>回复内容：</Text>
                            <div style={{ marginTop: 4 }}>
                              {keyword.reply ? (
                                <Text ellipsis={{ tooltip: keyword.reply }}>
                                  {keyword.reply}
                                </Text>
                              ) : (
                                <Text type="secondary" italic>
                                  不回复（屏蔽消息）
                                </Text>
                              )}
                            </div>
                          </div>
                        )}
                      </Card>
                    </Col>
                  ))}
                </Row>
              )}
            </div>
          </Spin>
        </Card>
      )}

      {/* 添加/编辑关键词模态框 */}
      <Modal
        title={
          <Space>
            {editingKeyword ? <EditOutlined /> : <PlusOutlined />}
            <span>{editingKeyword ? '编辑关键词' : '添加关键词'}</span>
          </Space>
        }
        open={addModalVisible}
        onCancel={() => {
          setAddModalVisible(false);
          setEditingKeyword(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={editingKeyword ? handleEditKeyword : handleAddKeyword}
        >
          <Form.Item
            label="关键词"
            name="keyword"
            rules={[{ required: true, message: '请输入关键词' }]}
          >
            <Input placeholder="例如：你好" />
          </Form.Item>

          <Form.Item
            label="自动回复内容"
            name="reply"
            help="留空表示不回复，可用于屏蔽特定消息"
          >
            <TextArea
              rows={3}
              placeholder="例如：您好，欢迎咨询！支持变量：{send_user_name}、{send_user_id}、{send_message}"
            />
          </Form.Item>

          <Form.Item
            label="关联商品"
            name="item_id"
            help="选择商品后，该关键词仅在对应商品的聊天中生效"
          >
            <Select
              placeholder="选择商品或留空表示通用关键词"
              allowClear
            >
              {items.map(item => (
                <Option key={item.item_id} value={item.item_id}>
                  <Tooltip title={item.title}>
                    <Text ellipsis style={{ maxWidth: 300 }}>
                      {item.title}
                    </Text>
                  </Tooltip>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setAddModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingKeyword ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加图片关键词模态框 */}
      <Modal
        title={
          <Space>
            <FileImageOutlined />
            <span>添加图片关键词</span>
          </Space>
        }
        open={imageModalVisible}
        onCancel={() => {
          setImageModalVisible(false);
          imageForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Alert
          message="图片关键词说明"
          description="上传图片作为关键词，当用户发送相同或相似图片时触发自动回复。图片关键词添加后不支持编辑，只能删除后重新添加。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={imageForm}
          layout="vertical"
          onFinish={async (values) => {
            try {
              const formData = new FormData();
              formData.append('keyword', values.keyword);
              formData.append('item_id', values.item_id || '');
              formData.append('image', values.image.file);

              const response = await keywordApi.uploadImageKeyword(formData);
              if (response.data.success) {
                message.success('图片关键词添加成功');
                setImageModalVisible(false);
                imageForm.resetFields();
                loadKeywords(selectedAccount);
              }
            } catch (error: any) {
              message.error(error.message || '添加图片关键词失败');
            }
          }}
        >
          <Form.Item
            label="关键词名称"
            name="keyword"
            rules={[{ required: true, message: '请输入关键词名称' }]}
          >
            <Input placeholder="为这个图片关键词起个名字" />
          </Form.Item>

          <Form.Item
            label="关联商品"
            name="item_id"
            help="选择商品后，该图片关键词仅在对应商品的聊天中生效"
          >
            <Select
              placeholder="选择商品或留空表示通用关键词"
              allowClear
            >
              {items.map(item => (
                <Option key={item.item_id} value={item.item_id}>
                  <Tooltip title={item.title}>
                    <Text ellipsis style={{ maxWidth: 300 }}>
                      {item.title}
                    </Text>
                  </Tooltip>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="上传图片"
            name="image"
            rules={[{ required: true, message: '请上传图片' }]}
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
              accept="image/*"
            >
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>
            </Upload>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setImageModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                添加
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 导入关键词模态框 */}
      <Modal
        title={
          <Space>
            <UploadOutlined />
            <span>导入关键词</span>
          </Space>
        }
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
        width={600}
      >
        <Alert
          message="导入说明"
          description="支持导入Excel文件或JSON文件。Excel文件需包含'关键词'、'回复内容'、'商品ID'列。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Upload
          accept=".xlsx,.xls,.json"
          beforeUpload={async (file) => {
            try {
              const formData = new FormData();
              formData.append('file', file);

              const response = await keywordApi.importKeywords(selectedAccount, formData);
              if (response.success) {
                message.success('关键词导入成功');
                setImportModalVisible(false);
                loadKeywords(selectedAccount);
              }
            } catch (error: any) {
              message.error(error.message || '导入关键词失败');
            }
            return false;
          }}
          showUploadList={false}
        >
          <Button icon={<UploadOutlined />} size="large" style={{ width: '100%', height: 100 }}>
            点击选择文件导入
          </Button>
        </Upload>
      </Modal>
    </div>
  );
};

export default AutoReply;
