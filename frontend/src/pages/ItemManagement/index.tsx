import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Table,
  Input,
  Select,
  Space,
  Popconfirm,
  message,
  Tag,
  Tooltip,
  Row,
  Col,
  InputNumber,
  Checkbox,
  Spin,
} from 'antd';
import {
  ShoppingOutlined,
  SearchOutlined,
  ReloadOutlined,
  DeleteOutlined,
  DownloadOutlined,
  CollectionOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { itemApi, accountApi } from '../../services/api';
import type { Item, Account, PaginationParams, SearchParams } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;

const ItemManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState<Item[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [pageNumber, setPageNumber] = useState(1);
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    pageSize: 20,
    total: 0,
  });

  // 加载账号列表
  const loadAccounts = async () => {
    try {
      const response = await accountApi.getAccounts();
      if (response.success) {
        setAccounts(response.data || []);
      }
    } catch (error) {
      console.error('加载账号列表失败:', error);
    }
  };

  // 加载商品列表
  const loadItems = async (params?: SearchParams & PaginationParams) => {
    try {
      setLoading(true);
      const searchParams = {
        keyword: searchKeyword,
        cookieId: selectedAccount,
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...params,
      };

      const response = await itemApi.getItems(searchParams);
      if (response.success) {
        setItems(response.data?.items || []);
        setPagination(prev => ({
          ...prev,
          total: response.data?.total || 0,
        }));
      }
    } catch (error: any) {
      console.error('加载商品列表失败:', error);
      message.error('加载商品列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取指定页商品
  const getItemsByPage = async () => {
    if (!selectedAccount) {
      message.warning('请先选择一个账号');
      return;
    }

    if (pageNumber < 1) {
      message.warning('页码必须大于0');
      return;
    }

    try {
      setLoading(true);
      const response = await itemApi.getItemsByAccount(selectedAccount, pageNumber);
      if (response.success) {
        message.success(`成功获取第${pageNumber}页商品信息`);
        loadItems(); // 刷新列表
      }
    } catch (error: any) {
      message.error(error.message || '获取商品信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取所有页商品
  const getAllItemsFromAccount = async () => {
    if (!selectedAccount) {
      message.warning('请先选择一个账号');
      return;
    }

    try {
      setLoading(true);
      const response = await itemApi.getAllItemsFromAccount(selectedAccount);
      if (response.success) {
        message.success('成功获取所有商品信息');
        loadItems(); // 刷新列表
      }
    } catch (error: any) {
      message.error(error.message || '获取商品信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除商品
  const handleDeleteItem = async (id: string) => {
    try {
      const response = await itemApi.deleteItem(id);
      if (response.success) {
        message.success('商品删除成功');
        loadItems();
      }
    } catch (error: any) {
      message.error(error.message || '删除商品失败');
    }
  };

  // 批量删除商品
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的商品');
      return;
    }

    try {
      const response = await itemApi.batchDeleteItems(selectedRowKeys);
      if (response.success) {
        message.success(`成功删除 ${selectedRowKeys.length} 个商品`);
        setSelectedRowKeys([]);
        loadItems();
      }
    } catch (error: any) {
      message.error(error.message || '批量删除失败');
    }
  };

  // 搜索商品
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    loadItems({ page: 1 });
  };

  // 重置搜索
  const handleReset = () => {
    setSearchKeyword('');
    setSelectedAccount('');
    setPagination(prev => ({ ...prev, page: 1 }));
    loadItems({ keyword: '', cookieId: '', page: 1 });
  };

  useEffect(() => {
    loadAccounts();
    loadItems();
  }, []);

  return (
    <div>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <ShoppingOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              商品管理
            </Title>
            <Text type="secondary">管理各账号的商品信息</Text>
          </div>
          <div className="header-extra">
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => loadItems()}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 筛选和搜索区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8} lg={6}>
            <div>
              <Text strong>筛选账号</Text>
              <Select
                style={{ width: '100%', marginTop: 4 }}
                placeholder="所有账号"
                allowClear
                value={selectedAccount || undefined}
                onChange={(value) => setSelectedAccount(value || '')}
              >
                {accounts.map(account => (
                  <Option key={account.id} value={account.id}>
                    {account.id}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <div>
              <Text strong>页码</Text>
              <InputNumber
                style={{ width: '100%', marginTop: 4 }}
                min={1}
                value={pageNumber}
                onChange={(value) => setPageNumber(value || 1)}
                placeholder="页码"
              />
            </div>
          </Col>

          <Col xs={24} sm={24} md={8} lg={12}>
            <div>
              <Text strong>操作</Text>
              <div style={{ marginTop: 4 }}>
                <Space wrap>
                  <Button
                    type="primary"
                    icon={<DownloadOutlined />}
                    onClick={getItemsByPage}
                    loading={loading}
                  >
                    获取指定页
                  </Button>
                  <Button
                    icon={<CollectionOutlined />}
                    onClick={getAllItemsFromAccount}
                    loading={loading}
                  >
                    获取所有页
                  </Button>
                </Space>
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 商品列表 */}
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space>
              <ShoppingOutlined />
              <span>商品列表</span>
              <Text type="secondary">(自动发货根据商品标题和商品详情匹配关键字)</Text>
            </Space>
            <Space>
              <Input
                placeholder="搜索商品标题或详情..."
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                onPressEnter={handleSearch}
                style={{ width: 300 }}
                suffix={
                  <Button
                    type="text"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                    size="small"
                  />
                }
              />
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </div>
        }
        extra={
          <Popconfirm
            title="确定要删除选中的商品吗？"
            description={`将删除 ${selectedRowKeys.length} 个商品，此操作不可恢复。`}
            onConfirm={handleBatchDelete}
            disabled={selectedRowKeys.length === 0}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              disabled={selectedRowKeys.length === 0}
            >
              批量删除 ({selectedRowKeys.length})
            </Button>
          </Popconfirm>
        }
      >
        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            preserveSelectedRowKeys: true,
          }}
          columns={[
            {
              title: '账号ID',
              dataIndex: 'cookie_id',
              key: 'cookie_id',
              width: '10%',
              render: (text: string) => (
                <Text code>{text}</Text>
              ),
            },
            {
              title: '商品ID',
              dataIndex: 'item_id',
              key: 'item_id',
              width: '12%',
              render: (text: string) => (
                <Text code copyable={{ text }}>
                  {text}
                </Text>
              ),
            },
            {
              title: '商品标题',
              dataIndex: 'title',
              key: 'title',
              width: '20%',
              render: (text: string) => (
                <Tooltip title={text}>
                  <Text ellipsis style={{ maxWidth: 200 }}>
                    {text}
                  </Text>
                </Tooltip>
              ),
            },
            {
              title: '商品详情',
              dataIndex: 'detail',
              key: 'detail',
              width: '20%',
              render: (text: string) => (
                <Tooltip title={text}>
                  <Text ellipsis style={{ maxWidth: 200 }}>
                    {text}
                  </Text>
                </Tooltip>
              ),
            },
            {
              title: '商品价格',
              dataIndex: 'price',
              key: 'price',
              width: '10%',
              render: (text: string) => (
                <Text strong style={{ color: '#f50' }}>
                  ¥{text}
                </Text>
              ),
            },
            {
              title: '多规格',
              dataIndex: 'multi_spec',
              key: 'multi_spec',
              width: '8%',
              render: (value: boolean) => (
                <Tag color={value ? 'success' : 'default'}>
                  {value ? '是' : '否'}
                </Tag>
              ),
            },
            {
              title: '多数量发货',
              dataIndex: 'multi_quantity_delivery',
              key: 'multi_quantity_delivery',
              width: '10%',
              render: (value: boolean) => (
                <Tag color={value ? 'processing' : 'default'}>
                  {value ? '是' : '否'}
                </Tag>
              ),
            },
            {
              title: '更新时间',
              dataIndex: 'updated_at',
              key: 'updated_at',
              width: '12%',
              render: (time: string) => (
                <Text type="secondary">
                  {dayjs(time).format('MM-DD HH:mm')}
                </Text>
              ),
            },
            {
              title: '操作',
              key: 'action',
              width: '8%',
              render: (_, record: Item) => (
                <Popconfirm
                  title="确定要删除这个商品吗？"
                  onConfirm={() => handleDeleteItem(record.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    type="text"
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                  />
                </Popconfirm>
              ),
            },
          ]}
          dataSource={items}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.page,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              const newPagination = { page, pageSize, total: pagination.total };
              setPagination(newPagination);
              loadItems(newPagination);
            },
          }}
          scroll={{ x: 1200 }}
          locale={{
            emptyText: (
              <div style={{ padding: '40px 0', textAlign: 'center' }}>
                <ShoppingOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                <div>
                  <Title level={4} type="secondary">暂无商品</Title>
                  <Text type="secondary">请先选择账号并获取商品信息</Text>
                </div>
              </div>
            ),
          }}
        />
      </Card>
    </div>
  );
};

export default ItemManagement;
