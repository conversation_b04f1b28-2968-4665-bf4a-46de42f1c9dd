import React from 'react';
import { Card, Typography } from 'antd';
import { CommentOutlined } from '@ant-design/icons';

const { Title } = Typography;

const ItemReply: React.FC = () => {
  return (
    <div>
      <div className="page-header">
        <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
          <CommentOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          指定商品回复
        </Title>
      </div>
      <Card>
        <p>指定商品回复功能正在开发中...</p>
      </Card>
    </div>
  );
};

export default ItemReply;
