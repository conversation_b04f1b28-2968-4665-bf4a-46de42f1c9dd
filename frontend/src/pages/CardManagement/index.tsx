import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Table,
  Space,
  Tag,
  Statistic,
  Row,
  Col,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Popconfirm,
} from 'antd';
import {
  CreditCardOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ApiOutlined,
  FileTextOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import { cardApi } from '../../services/api';
import type { Card as CardType } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CardManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [cards, setCards] = useState<CardType[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCard, setEditingCard] = useState<CardType | null>(null);
  const [form] = Form.useForm();

  // 加载卡券列表
  const loadCards = async () => {
    try {
      setLoading(true);
      const response = await cardApi.getCards();
      if (response.success) {
        setCards(response.data || []);
      }
    } catch (error: any) {
      console.error('加载卡券列表失败:', error);
      message.error('加载卡券列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 添加卡券
  const handleAddCard = async (values: any) => {
    try {
      const response = await cardApi.addCard(values);
      if (response.success) {
        message.success('卡券添加成功');
        setModalVisible(false);
        form.resetFields();
        loadCards();
      }
    } catch (error: any) {
      message.error(error.message || '添加卡券失败');
    }
  };

  // 编辑卡券
  const handleEditCard = async (values: any) => {
    if (!editingCard) return;

    try {
      const response = await cardApi.updateCard(editingCard.id, values);
      if (response.success) {
        message.success('卡券更新成功');
        setModalVisible(false);
        setEditingCard(null);
        form.resetFields();
        loadCards();
      }
    } catch (error: any) {
      message.error(error.message || '更新卡券失败');
    }
  };

  // 删除卡券
  const handleDeleteCard = async (id: string) => {
    try {
      const response = await cardApi.deleteCard(id);
      if (response.success) {
        message.success('卡券删除成功');
        loadCards();
      }
    } catch (error: any) {
      message.error(error.message || '删除卡券失败');
    }
  };

  // 打开编辑模态框
  const openEditModal = (card: CardType) => {
    setEditingCard(card);
    form.setFieldsValue(card);
    setModalVisible(true);
  };

  useEffect(() => {
    loadCards();
  }, []);

  // 统计数据
  const stats = {
    total: cards.length,
    api: cards.filter(card => card.type === 'api').length,
    text: cards.filter(card => card.type === 'text').length,
    data: cards.filter(card => card.type === 'data').length,
  };

  // 卡券类型配置
  const typeConfig = {
    api: { color: 'blue', text: 'API', icon: <ApiOutlined /> },
    text: { color: 'green', text: '固定文字', icon: <FileTextOutlined /> },
    data: { color: 'orange', text: '批量数据', icon: <DatabaseOutlined /> },
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <CreditCardOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              卡券管理
            </Title>
            <Text type="secondary">管理虚拟商品的卡券数据，支持API、固定文字和批量数据</Text>
          </div>
          <div className="header-extra">
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setModalVisible(true)}
              >
                添加卡券
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadCards}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </div>
        </div>
      </div>

      <Row gutter={[16, 16]}>
        {/* 卡券列表 */}
        <Col xs={24} lg={16}>
          <Card
            title={
              <Space>
                <CreditCardOutlined />
                <span>卡券列表</span>
              </Space>
            }
          >
            <Table
              columns={[
                {
                  title: '卡券名称',
                  dataIndex: 'name',
                  key: 'name',
                  width: '20%',
                },
                {
                  title: '类型',
                  dataIndex: 'type',
                  key: 'type',
                  width: '12%',
                  render: (type: CardType['type']) => {
                    const config = typeConfig[type];
                    return (
                      <Tag color={config.color} icon={config.icon}>
                        {config.text}
                      </Tag>
                    );
                  },
                },
                {
                  title: '规格信息',
                  dataIndex: 'spec_info',
                  key: 'spec_info',
                  width: '15%',
                  render: (text: string) => text || '-',
                },
                {
                  title: '数据量',
                  dataIndex: 'data_count',
                  key: 'data_count',
                  width: '10%',
                  render: (count: number) => (
                    <Tag color="blue">{count}</Tag>
                  ),
                },
                {
                  title: '延时时间',
                  dataIndex: 'delay_time',
                  key: 'delay_time',
                  width: '10%',
                  render: (time: number) => `${time}秒`,
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  width: '8%',
                  render: (status: boolean) => (
                    <Tag color={status ? 'success' : 'default'}>
                      {status ? '启用' : '禁用'}
                    </Tag>
                  ),
                },
                {
                  title: '创建时间',
                  dataIndex: 'created_at',
                  key: 'created_at',
                  width: '15%',
                  render: (time: string) => (
                    <Text type="secondary">
                      {dayjs(time).format('MM-DD HH:mm')}
                    </Text>
                  ),
                },
                {
                  title: '操作',
                  key: 'action',
                  width: '10%',
                  render: (_, record: CardType) => (
                    <Space size="small">
                      <Button
                        type="text"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => openEditModal(record)}
                      />
                      <Popconfirm
                        title="确定要删除这个卡券吗？"
                        onConfirm={() => handleDeleteCard(record.id)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button
                          type="text"
                          danger
                          size="small"
                          icon={<DeleteOutlined />}
                        />
                      </Popconfirm>
                    </Space>
                  ),
                },
              ]}
              dataSource={cards}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
              }}
              locale={{
                emptyText: (
                  <div style={{ padding: '40px 0', textAlign: 'center' }}>
                    <CreditCardOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                    <div>
                      <Title level={4} type="secondary">暂无卡券数据</Title>
                      <Text type="secondary">点击"添加卡券"开始创建您的第一个卡券</Text>
                    </div>
                  </div>
                ),
              }}
            />
          </Card>
        </Col>

        {/* 统计信息 */}
        <Col xs={24} lg={8}>
          <Card title="统计信息">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="总卡券数"
                  value={stats.total}
                  prefix={<CreditCardOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="API类型"
                  value={stats.api}
                  prefix={<ApiOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="固定文字"
                  value={stats.text}
                  prefix={<FileTextOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="批量数据"
                  value={stats.data}
                  prefix={<DatabaseOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 添加/编辑卡券模态框 */}
      <Modal
        title={
          <Space>
            {editingCard ? <EditOutlined /> : <PlusOutlined />}
            <span>{editingCard ? '编辑卡券' : '添加卡券'}</span>
          </Space>
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingCard(null);
          form.resetFields();
        }}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={editingCard ? handleEditCard : handleAddCard}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="卡券名称"
                name="name"
                rules={[{ required: true, message: '请输入卡券名称' }]}
              >
                <Input placeholder="例如：QQ会员卡" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="卡券类型"
                name="type"
                rules={[{ required: true, message: '请选择卡券类型' }]}
              >
                <Select placeholder="选择卡券类型">
                  <Option value="api">
                    <Space>
                      <ApiOutlined />
                      <span>API接口</span>
                    </Space>
                  </Option>
                  <Option value="text">
                    <Space>
                      <FileTextOutlined />
                      <span>固定文字</span>
                    </Space>
                  </Option>
                  <Option value="data">
                    <Space>
                      <DatabaseOutlined />
                      <span>批量数据</span>
                    </Space>
                  </Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="规格信息"
                name="spec_info"
                help="用于匹配订单规格信息"
              >
                <Input placeholder="例如：1个月" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="延时时间（秒）"
                name="delay_time"
                rules={[{ required: true, message: '请输入延时时间' }]}
              >
                <InputNumber
                  min={0}
                  max={3600}
                  style={{ width: '100%' }}
                  placeholder="发货延时时间"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="状态"
            name="status"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) => {
              const type = getFieldValue('type');

              if (type === 'api') {
                return (
                  <Form.Item
                    label="API地址"
                    name="api_url"
                    rules={[{ required: true, message: '请输入API地址' }]}
                  >
                    <Input placeholder="https://api.example.com/get-card" />
                  </Form.Item>
                );
              }

              if (type === 'text') {
                return (
                  <Form.Item
                    label="固定文字内容"
                    name="fixed_text"
                    rules={[{ required: true, message: '请输入固定文字内容' }]}
                  >
                    <TextArea
                      rows={4}
                      placeholder="输入固定的卡券内容"
                    />
                  </Form.Item>
                );
              }

              if (type === 'data') {
                return (
                  <Form.Item
                    label="批量数据"
                    name="batch_data"
                    help="每行一个卡券数据"
                    rules={[{ required: true, message: '请输入批量数据' }]}
                  >
                    <TextArea
                      rows={6}
                      placeholder="每行输入一个卡券数据，例如：&#10;卡号1:密码1&#10;卡号2:密码2"
                    />
                  </Form.Item>
                );
              }

              return null;
            }}
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCard ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CardManagement;
