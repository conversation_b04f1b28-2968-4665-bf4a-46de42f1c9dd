/* 主布局样式 */
.main-layout {
  min-height: 100vh;
  width: 100%;
  display: flex;
}

/* 重置 Ant Design 布局样式 */
.main-layout .ant-layout {
  background: transparent;
}

.main-layout .ant-layout-sider {
  background: #001529 !important;
}

/* 侧边栏样式 */
.layout-sider {
  position: fixed !important;
  left: 0;
  top: 0;
  bottom: 0;
  height: 100vh;
  z-index: 100;
  overflow: auto;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-menu-area {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 8px;
}

/* 菜单区域滚动条样式 */
.sidebar-menu-area::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu-area::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-menu-area::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.sidebar-menu-area::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.sidebar-footer {
  flex-shrink: 0;
  border-top: 1px solid #303030;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #303030;
}

.sidebar-brand {
  display: flex;
  align-items: center;
  color: #fff;
  text-decoration: none;
}

.brand-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 12px;
}

.brand-text {
  font-size: 18px;
  font-weight: 600;
  white-space: nowrap;
}

/* 菜单样式 */
.main-menu,
.admin-menu,
.other-menu,
.system-menu {
  border-right: none;
  background: transparent;
}

.main-menu {
  margin-bottom: 8px;
}

.admin-menu,
.other-menu {
  margin-bottom: 8px;
}

.system-menu {
  margin-bottom: 0;
}

.menu-divider {
  margin: 12px 0 8px 0;
  border-color: #303030;
}

.divider-text {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.45);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* 登出按钮 */
.logout-section {
  padding: 12px 16px;
  border-top: 1px solid #303030;
}

.logout-btn {
  color: rgba(255, 255, 255, 0.65) !important;
  border: 1px solid #303030 !important;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  background-color: rgba(255, 77, 79, 0.1) !important;
}

.logout-btn .anticon {
  color: inherit;
}

/* 版本信息 */
.version-info {
  padding: 12px 16px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.2);
}

.version-full {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.version-badge {
  display: flex;
  align-items: center;
  justify-content: center;
}

.copyright {
  text-align: center;
}

.version-collapsed {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 头部样式 */
.layout-header {
  background: #fff;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 99;
  display: flex;
  align-items: center;
  width: 100%;
}

.trigger-btn {
  font-size: 18px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.65);
  transition: all 0.3s;
}

.trigger-btn:hover {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

/* 内容区域样式 */
.layout-content {
  margin-left: 250px;
  transition: margin-left 0.2s;
  min-height: 100vh;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.layout-content.collapsed {
  margin-left: 80px;
}

.layout-main-content {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
  width: 100%;
  overflow-x: auto;
  flex: 1;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .layout-content {
    margin-left: 0 !important;
  }

  .layout-content.collapsed {
    margin-left: 0 !important;
  }

  .layout-main-content {
    padding: 16px;
  }

  .layout-sider {
    display: none;
  }
}

/* 抽屉内菜单样式 */
.ant-drawer-body .main-menu,
.ant-drawer-body .admin-menu,
.ant-drawer-body .other-menu,
.ant-drawer-body .system-menu {
  background: transparent;
}

.ant-drawer-body .menu-divider {
  border-color: #303030;
}

.ant-drawer-body .version-info {
  border-top: 1px solid #303030;
}

/* 菜单项悬停效果 */
.ant-menu-dark .ant-menu-item:hover,
.ant-menu-dark .ant-menu-submenu-title:hover {
  background-color: rgba(24, 144, 255, 0.2);
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff;
}

/* 滚动条样式 */
.layout-sider::-webkit-scrollbar {
  width: 6px;
}

.layout-sider::-webkit-scrollbar-track {
  background: #001529;
}

.layout-sider::-webkit-scrollbar-thumb {
  background: #303030;
  border-radius: 3px;
}

.layout-sider::-webkit-scrollbar-thumb:hover {
  background: #404040;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .content-wrapper {
    max-width: 100%;
  }
}

@media (max-width: 576px) {
  .layout-main-content {
    padding: 12px;
  }
  
  .layout-header {
    padding: 0 12px;
  }
}
