import React, { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout,
  Menu,
  Button,
  Drawer,
  Typography,
  Divider,
  Badge,
  Space,
  message,
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MessageOutlined,
} from '@ant-design/icons';
import { menuItems, adminMenuItems, otherMenuItems, systemMenuItems } from '../../config/menu';
import type { MenuItem } from '../../types';
import './MainLayout.css';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [systemVersion, setSystemVersion] = useState('加载中...');
  const navigate = useNavigate();
  const location = useLocation();

  // 检查是否为移动端
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 检查用户权限
  useEffect(() => {
    const userRole = localStorage.getItem('user_role');
    setIsAdmin(userRole === 'admin');
  }, []);

  // 获取系统版本
  useEffect(() => {
    // TODO: 从API获取系统版本
    setSystemVersion('v1.0.0');
  }, []);

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    if (key === 'logout') {
      handleLogout();
      return;
    }

    navigate(`/${key}`);
    
    // 移动端关闭抽屉
    if (isMobile) {
      setMobileDrawerVisible(false);
    }
  };

  // 处理登出
  const handleLogout = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_role');
    message.success('已成功登出');
    window.location.href = '/login.html';
  };

  // 获取当前选中的菜单项
  const getCurrentMenuKey = () => {
    const path = location.pathname;
    return path.substring(1) || 'dashboard';
  };

  // 构建菜单项
  const buildMenuItems = (items: MenuItem[]) => {
    return items.map(item => ({
      key: item.key,
      icon: item.icon,
      label: item.label,
      children: item.children ? buildMenuItems(item.children) : undefined,
    }));
  };

  // 侧边栏内容
  const siderContent = (
    <div className="sidebar-content">
      {/* 侧边栏头部 */}
      <div className="sidebar-header">
        <div className="sidebar-brand">
          <MessageOutlined className="brand-icon" />
          {!collapsed && <span className="brand-text">闲鱼管理系统</span>}
        </div>
      </div>

      {/* 主要菜单 */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[getCurrentMenuKey()]}
        items={buildMenuItems(menuItems)}
        onClick={handleMenuClick}
        className="main-menu"
      />

      {/* 管理员菜单 */}
      {isAdmin && (
        <>
          <Divider className="menu-divider">
            <Text type="secondary" className="divider-text">管理员功能</Text>
          </Divider>
          <Menu
            theme="dark"
            mode="inline"
            selectedKeys={[getCurrentMenuKey()]}
            items={buildMenuItems(adminMenuItems)}
            onClick={handleMenuClick}
            className="admin-menu"
          />
        </>
      )}

      {/* 其他菜单 */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[getCurrentMenuKey()]}
        items={buildMenuItems(otherMenuItems)}
        onClick={handleMenuClick}
        className="other-menu"
      />

      {/* 系统操作菜单 */}
      <Divider className="menu-divider">
        <Text type="secondary" className="divider-text">系统操作</Text>
      </Divider>
      <Menu
        theme="dark"
        mode="inline"
        items={buildMenuItems(systemMenuItems)}
        onClick={handleMenuClick}
        className="system-menu"
      />

      {/* 版本信息 */}
      {!collapsed && (
        <div className="version-info">
          <Badge color="blue" text={`版本: ${systemVersion}`} />
        </div>
      )}
    </div>
  );

  return (
    <Layout className="main-layout">
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          className="layout-sider"
          width={250}
          collapsedWidth={80}
        >
          {siderContent}
        </Sider>
      )}

      {/* 移动端抽屉 */}
      {isMobile && (
        <Drawer
          title={
            <Space>
              <MessageOutlined />
              <span>闲鱼管理系统</span>
            </Space>
          }
          placement="left"
          onClose={() => setMobileDrawerVisible(false)}
          open={mobileDrawerVisible}
          bodyStyle={{ padding: 0, backgroundColor: '#001529' }}
          headerStyle={{ backgroundColor: '#001529', borderBottom: '1px solid #303030' }}
          width={250}
        >
          {siderContent}
        </Drawer>
      )}

      <Layout className="layout-content">
        {/* 头部 */}
        <Header className="layout-header">
          <Button
            type="text"
            icon={
              isMobile ? (
                <MenuUnfoldOutlined />
              ) : collapsed ? (
                <MenuUnfoldOutlined />
              ) : (
                <MenuFoldOutlined />
              )
            }
            onClick={() => {
              if (isMobile) {
                setMobileDrawerVisible(true);
              } else {
                setCollapsed(!collapsed);
              }
            }}
            className="trigger-btn"
          />
        </Header>

        {/* 主内容区域 */}
        <Content className="layout-main-content">
          <div className="content-wrapper">
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
